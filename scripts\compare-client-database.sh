#!/bin/bash

# Script para comparar estructura de BD con cliente
# Uso: ./compare-client-database.sh CLIENT_HOST CLIENT_USER CLIENT_PASS CLIENT_DB

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Verificar parámetros
if [ $# -ne 4 ]; then
    error "Uso: $0 CLIENT_HOST CLIENT_USER CLIENT_PASS CLIENT_DB"
    exit 1
fi

CLIENT_HOST=$1
CLIENT_USER=$2
CLIENT_PASS=$3
CLIENT_DB=$4

# Crear directorio de trabajo
WORK_DIR="./scripts/db-comparison"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
mkdir -p "$WORK_DIR"

log "Iniciando comparación con base de datos del cliente..."
log "Cliente: $CLIENT_USER@$CLIENT_HOST/$CLIENT_DB"

# 1. Exportar estructura del cliente
log "Exportando estructura de base de datos del cliente..."
CLIENT_STRUCTURE="$WORK_DIR/client-structure-$TIMESTAMP.sql"

if mysqldump -h "$CLIENT_HOST" -u "$CLIENT_USER" -p"$CLIENT_PASS" \
    --no-data --routines --triggers --single-transaction \
    --set-gtid-purged=OFF "$CLIENT_DB" > "$CLIENT_STRUCTURE" 2>/dev/null; then
    success "Estructura del cliente exportada: $CLIENT_STRUCTURE"
else
    error "No se pudo conectar o exportar la estructura del cliente"
    exit 1
fi

# 2. Generar estructura local desde entidades
log "Generando estructura local desde entidades Doctrine..."
LOCAL_STRUCTURE="$WORK_DIR/local-structure-$TIMESTAMP.sql"

if make console doctrine:schema:create --dump-sql > "$LOCAL_STRUCTURE" 2>/dev/null; then
    success "Estructura local generada: $LOCAL_STRUCTURE"
else
    error "No se pudo generar la estructura local"
    exit 1
fi

# 3. Generar diferencias con BD local actual
log "Generando diferencias con base de datos local actual..."
LOCAL_DIFF="$WORK_DIR/local-diff-$TIMESTAMP.sql"

make console doctrine:schema:update --dump-sql --complete > "$LOCAL_DIFF" 2>/dev/null || true
success "Diferencias locales generadas: $LOCAL_DIFF"

# 4. Comparar estructuras
log "Comparando estructuras..."
COMPARISON_FILE="$WORK_DIR/structure-comparison-$TIMESTAMP.txt"

echo "=== COMPARACIÓN DE ESTRUCTURAS DE BASE DE DATOS ===" > "$COMPARISON_FILE"
echo "Fecha: $(date)" >> "$COMPARISON_FILE"
echo "Cliente: $CLIENT_USER@$CLIENT_HOST/$CLIENT_DB" >> "$COMPARISON_FILE"
echo "" >> "$COMPARISON_FILE"

# Usar diff para comparar (ignorando comentarios y espacios)
if diff -u --ignore-blank-lines \
    <(grep -v '^--' "$CLIENT_STRUCTURE" | grep -v '^/\*' | sed '/^$/d') \
    <(grep -v '^--' "$LOCAL_STRUCTURE" | grep -v '^/\*' | sed '/^$/d') \
    >> "$COMPARISON_FILE" 2>/dev/null; then
    success "Las estructuras son idénticas"
    echo "✅ ESTRUCTURAS IDÉNTICAS" >> "$COMPARISON_FILE"
else
    warning "Se encontraron diferencias en las estructuras"
    echo "⚠️ SE ENCONTRARON DIFERENCIAS" >> "$COMPARISON_FILE"
fi

# 5. Analizar tablas específicas
log "Analizando tablas específicas..."
TABLES_ANALYSIS="$WORK_DIR/tables-analysis-$TIMESTAMP.txt"

echo "=== ANÁLISIS DE TABLAS ===" > "$TABLES_ANALYSIS"
echo "" >> "$TABLES_ANALYSIS"

# Extraer nombres de tablas del cliente
CLIENT_TABLES=$(mysql -h "$CLIENT_HOST" -u "$CLIENT_USER" -p"$CLIENT_PASS" \
    -D "$CLIENT_DB" -e "SHOW TABLES;" -s 2>/dev/null | tail -n +1)

echo "Tablas en base de datos del cliente:" >> "$TABLES_ANALYSIS"
echo "$CLIENT_TABLES" | sed 's/^/- /' >> "$TABLES_ANALYSIS"
echo "" >> "$TABLES_ANALYSIS"

# Contar tablas
CLIENT_TABLE_COUNT=$(echo "$CLIENT_TABLES" | wc -l)
echo "Total de tablas en cliente: $CLIENT_TABLE_COUNT" >> "$TABLES_ANALYSIS"

# 6. Verificar migraciones
log "Verificando estado de migraciones..."
MIGRATIONS_STATUS="$WORK_DIR/migrations-status-$TIMESTAMP.txt"

echo "=== ESTADO DE MIGRACIONES LOCALES ===" > "$MIGRATIONS_STATUS"
make console doctrine:migrations:list >> "$MIGRATIONS_STATUS" 2>/dev/null || true

# Verificar si existe tabla de migraciones en cliente
if mysql -h "$CLIENT_HOST" -u "$CLIENT_USER" -p"$CLIENT_PASS" \
    -D "$CLIENT_DB" -e "DESCRIBE migration_versions;" >/dev/null 2>&1; then
    
    echo "" >> "$MIGRATIONS_STATUS"
    echo "=== MIGRACIONES EN CLIENTE ===" >> "$MIGRATIONS_STATUS"
    mysql -h "$CLIENT_HOST" -u "$CLIENT_USER" -p"$CLIENT_PASS" \
        -D "$CLIENT_DB" -e "SELECT version, executed_at FROM migration_versions ORDER BY executed_at;" \
        >> "$MIGRATIONS_STATUS" 2>/dev/null || true
else
    echo "" >> "$MIGRATIONS_STATUS"
    echo "⚠️ Tabla migration_versions no encontrada en cliente" >> "$MIGRATIONS_STATUS"
fi

# 7. Generar reporte final
log "Generando reporte final..."
REPORT_FILE="$WORK_DIR/client-comparison-report-$TIMESTAMP.md"

cat > "$REPORT_FILE" << EOF
# Reporte de Comparación con Cliente

**Fecha:** $(date)  
**Cliente:** $CLIENT_USER@$CLIENT_HOST/$CLIENT_DB  
**Timestamp:** $TIMESTAMP

## Archivos Generados

- [Estructura Cliente](./$(basename "$CLIENT_STRUCTURE"))
- [Estructura Local](./$(basename "$LOCAL_STRUCTURE"))
- [Diferencias Locales](./$(basename "$LOCAL_DIFF"))
- [Comparación](./$(basename "$COMPARISON_FILE"))
- [Análisis de Tablas](./$(basename "$TABLES_ANALYSIS"))
- [Estado Migraciones](./$(basename "$MIGRATIONS_STATUS"))

## Resumen

$(if diff -q "$CLIENT_STRUCTURE" "$LOCAL_STRUCTURE" >/dev/null 2>&1; then echo "✅ **Las estructuras son idénticas**"; else echo "⚠️ **Se encontraron diferencias en las estructuras**"; fi)

**Tablas en cliente:** $CLIENT_TABLE_COUNT

## Próximos Pasos

### Si hay diferencias:

1. **Revisar diferencias:**
   \`\`\`bash
   diff -u $(basename "$CLIENT_STRUCTURE") $(basename "$LOCAL_STRUCTURE")
   \`\`\`

2. **Generar migración para sincronizar:**
   \`\`\`bash
   make console doctrine:migrations:diff
   \`\`\`

3. **Aplicar en cliente (¡CUIDADO!):**
   \`\`\`bash
   # Primero en entorno de prueba
   make console doctrine:migrations:migrate --dry-run
   make console doctrine:migrations:migrate
   \`\`\`

### Para sincronizar migraciones:

\`\`\`bash
# Marcar migraciones como ejecutadas en cliente
make console doctrine:migrations:version --add --all --no-interaction
\`\`\`

## ⚠️ Advertencias

- **SIEMPRE** hacer backup antes de aplicar cambios
- Probar en entorno de desarrollo/staging primero
- Revisar manualmente todas las diferencias
- Considerar el impacto en datos existentes

EOF

success "Reporte generado: $REPORT_FILE"

# Mostrar resumen
echo ""
echo "📊 RESUMEN DE COMPARACIÓN"
echo "========================="
echo "Cliente: $CLIENT_USER@$CLIENT_HOST/$CLIENT_DB"
echo "Tablas en cliente: $CLIENT_TABLE_COUNT"
echo ""
echo "📁 Archivos generados en: $WORK_DIR"
echo "📋 Ver reporte completo: $REPORT_FILE"
echo ""

if [ -s "$LOCAL_DIFF" ] && [ "$(wc -l < "$LOCAL_DIFF")" -gt 1 ]; then
    warning "Tu base de datos local tiene diferencias con las entidades"
    echo "   Ejecuta: make console doctrine:schema:update --force --complete"
fi

log "Comparación completada exitosamente!"
