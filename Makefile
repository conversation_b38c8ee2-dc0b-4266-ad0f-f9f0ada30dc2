PHP_CONTAINER=easylearning-backend-php
USER_ID:=$(shell id -u)
GROUP_ID:=$(shell id -g)

start:
	UID=${USER_ID} GID=${GROUP_ID} docker compose up --build -d --remove-orphans

stop:
	UID=${USER_ID} GID=${GROUP_ID} docker compose stop

destroy:
	UID=${USER_ID} GID=${GROUP_ID} docker compose down --remove-orphans

rebuild:
	UID=${USER_ID} GID=${GROUP_ID} docker compose build --pull --force-rm --no-cache
	make install
	make start

restart:
	make stop
	make start

install:
	make stop
	make start
	make composer-install
	make databasediff-force
	make catalogues

composer-install:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer install

composer-update:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer update

composer-require:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require $(filter-out $@,$(<PERSON><PERSON>CMDG<PERSON>LS))

composer-req:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require $(filter-out $@,$(MAKECMDGOALS))

composer-require-dev:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} composer require --dev $(filter-out $@,$(MAKECMDGOALS))

fix-code-project:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} bash -c "vendor/bin/php-cs-fixer fix --allow-risky=yes --config=.php-cs-fixer.php"

fix-code-staged:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} bash -c "git status -s | grep -e '^[ AM]' | cut -c4- | tr '\n' ' ' | xargs -r vendor/bin/php-cs-fixer --verbose --config=.php-cs-fixer.php fix --allow-risky=yes"

test:
	make clean-test-database
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console doctrine:migrations:migrate --all-or-nothing --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console catalogs:update --new --no-interaction --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./vendor/bin/phpunit

test-single:
	make clean-test-database
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console doctrine:migrations:migrate --all-or-nothing --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} php -d memory_limit=512M bin/console catalogs:update --new --no-interaction --env=test
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./vendor/bin/phpunit --filter $(filter-out $@,$(MAKECMDGOALS))

clean-cache:
	@rm -rf var/cache/*
	@if [ -d vendor ]; then UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} ./bin/console cache:warmup; fi

bash:
	UID=${USER_ID} GID=${GROUP_ID} docker exec -it ${PHP_CONTAINER} bash

console:
	UID=${USER_ID} GID=${GROUP_ID} docker exec ${PHP_CONTAINER} symfony console  $(filter-out $@,$(MAKECMDGOALS))

databasediff:
	UID=${USER_ID} GID=${GROUP_ID} docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete

databasediff-force:
	docker exec easylearning-backend-php symfony console doctrine:schema:update --force --complete

# Comandos alternativos sin UID/GID para Windows
databasediff-win:
	docker exec easylearning-backend-php symfony console doctrine:schema:update --dump-sql --complete

console-win:
	docker exec easylearning-backend-php symfony console $(filter-out $@,$(MAKECMDGOALS))

schema-validate:
	docker exec easylearning-backend-php symfony console doctrine:schema:validate

migrations-status:
	docker exec easylearning-backend-php symfony console doctrine:migrations:status

migrations-list:
	docker exec easylearning-backend-php symfony console doctrine:migrations:list

migrations-diff:
	docker exec easylearning-backend-php symfony console doctrine:migrations:diff

# Comandos de análisis completo (Doctrine + V2)
analyze-structure:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --include-v2

analyze-structure-v2-only:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --v2-only

analyze-structure-client:
	docker exec easylearning-backend-php symfony console app:database:compare-structure $(CLIENT_DSN) --include-v2

analyze-structure-report:
	docker exec easylearning-backend-php symfony console app:database:compare-structure --include-v2 -o ./scripts/db-comparison/structure-report-$(shell date +%Y%m%d_%H%M%S).txt

clean-test-database:
	docker exec -i easylearning-database mysql -u root -pdocker -e "DROP DATABASE IF EXISTS easylearning_test; CREATE DATABASE IF NOT EXISTS easylearning_test;"

catalogues:
	docker exec easylearning-backend-php symfony console catalogs:update

catalogues-new:
	docker exec easylearning-backend-php symfony console catalogs:update --new

locales:
	docker exec easylearning-backend-php symfony console translation:pull --force --format=yaml

set-diploma:
	docker exec easylearning-backend-php symfony console app:set-diploma

deploy:
	node scripts/deploy.js

# Handle arguments after the target
%:
	@:
