<?php

declare(strict_types=1);

namespace App\Command\Database;

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\Schema\Comparator;
use Doctrine\DBAL\Schema\Schema;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\Tools\SchemaTool;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:database:compare-structure',
    description: 'Compara la estructura de entidades con una base de datos remota'
)]
class CompareStructureCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private Connection $connection
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addArgument('remote-dsn', InputArgument::REQUIRED, 'DSN de la base de datos remota (mysql://user:pass@host:port/database)')
            ->addOption('output-file', 'o', InputOption::VALUE_OPTIONAL, 'Archivo donde guardar el reporte')
            ->addOption('format', 'f', InputOption::VALUE_OPTIONAL, 'Formato de salida (text|json|sql)', 'text')
            ->addOption('only-differences', null, InputOption::VALUE_NONE, 'Mostrar solo las diferencias')
            ->setHelp('
Este comando compara la estructura definida en las entidades Doctrine
con una base de datos remota y muestra las diferencias.

Ejemplos:
  # Comparar con base de datos remota
  php bin/console app:database:compare-structure mysql://user:<EMAIL>:3306/client_db

  # Guardar reporte en archivo
  php bin/console app:database:compare-structure mysql://user:<EMAIL>:3306/client_db -o report.txt

  # Solo mostrar diferencias
  php bin/console app:database:compare-structure mysql://user:<EMAIL>:3306/client_db --only-differences
            ');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $remoteDsn = $input->getArgument('remote-dsn');
        $outputFile = $input->getOption('output-file');
        $format = $input->getOption('format');
        $onlyDifferences = $input->getOption('only-differences');

        $io->title('Comparación de Estructura de Base de Datos');

        try {
            // 1. Obtener esquema local desde entidades
            $io->section('Generando esquema local desde entidades...');
            $localSchema = $this->getLocalSchema();
            $io->success(sprintf('Esquema local generado con %d tablas', count($localSchema->getTables())));

            // 2. Conectar a base de datos remota
            $io->section('Conectando a base de datos remota...');
            $remoteConnection = $this->createRemoteConnection($remoteDsn);
            $remoteSchema = $this->getRemoteSchema($remoteConnection);
            $io->success(sprintf('Esquema remoto obtenido con %d tablas', count($remoteSchema->getTables())));

            // 3. Comparar esquemas
            $io->section('Comparando esquemas...');
            $differences = $this->compareSchemas($localSchema, $remoteSchema);

            // 4. Generar reporte
            $report = $this->generateReport($localSchema, $remoteSchema, $differences, $format, $onlyDifferences);

            // 5. Mostrar/guardar resultado
            if ($outputFile) {
                file_put_contents($outputFile, $report);
                $io->success("Reporte guardado en: $outputFile");
            } else {
                $output->write($report);
            }

            // 6. Resumen
            $this->showSummary($io, $differences, $onlyDifferences);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Error durante la comparación: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }

    private function getLocalSchema(): Schema
    {
        $metadata = $this->entityManager->getMetadataFactory()->getAllMetadata();
        $schemaTool = new SchemaTool($this->entityManager);
        
        return $schemaTool->getSchemaFromMetadata($metadata);
    }

    private function createRemoteConnection(string $dsn): Connection
    {
        $params = $this->parseDsn($dsn);
        
        return \Doctrine\DBAL\DriverManager::getConnection($params);
    }

    private function parseDsn(string $dsn): array
    {
        $parsed = parse_url($dsn);
        
        if (!$parsed) {
            throw new \InvalidArgumentException("DSN inválido: $dsn");
        }

        return [
            'driver' => $parsed['scheme'] === 'mysql' ? 'pdo_mysql' : $parsed['scheme'],
            'host' => $parsed['host'],
            'port' => $parsed['port'] ?? 3306,
            'dbname' => ltrim($parsed['path'], '/'),
            'user' => $parsed['user'],
            'password' => $parsed['pass'] ?? '',
            'charset' => 'utf8mb4',
        ];
    }

    private function getRemoteSchema(Connection $connection): Schema
    {
        return $connection->createSchemaManager()->introspectSchema();
    }

    private function compareSchemas(Schema $localSchema, Schema $remoteSchema): array
    {
        $comparator = new Comparator();
        $schemaDiff = $comparator->compareSchemas($remoteSchema, $localSchema);

        return [
            'new_tables' => $schemaDiff->getCreatedTables(),
            'dropped_tables' => $schemaDiff->getDroppedTables(),
            'altered_tables' => $schemaDiff->getAlteredTables(),
            'new_sequences' => $schemaDiff->getCreatedSequences(),
            'dropped_sequences' => $schemaDiff->getDroppedSequences(),
            'altered_sequences' => $schemaDiff->getAlteredSequences(),
        ];
    }

    private function generateReport(Schema $localSchema, Schema $remoteSchema, array $differences, string $format, bool $onlyDifferences): string
    {
        switch ($format) {
            case 'json':
                return $this->generateJsonReport($localSchema, $remoteSchema, $differences, $onlyDifferences);
            case 'sql':
                return $this->generateSqlReport($differences);
            default:
                return $this->generateTextReport($localSchema, $remoteSchema, $differences, $onlyDifferences);
        }
    }

    private function generateTextReport(Schema $localSchema, Schema $remoteSchema, array $differences, bool $onlyDifferences): string
    {
        $report = [];
        
        if (!$onlyDifferences) {
            $report[] = "=== REPORTE DE COMPARACIÓN DE ESTRUCTURA ===";
            $report[] = "Fecha: " . date('Y-m-d H:i:s');
            $report[] = "";
            $report[] = "Tablas locales: " . count($localSchema->getTables());
            $report[] = "Tablas remotas: " . count($remoteSchema->getTables());
            $report[] = "";
        }

        // Tablas nuevas
        if (!empty($differences['new_tables'])) {
            $report[] = "🆕 TABLAS NUEVAS (en local, no en remoto):";
            foreach ($differences['new_tables'] as $table) {
                $report[] = "  + " . $table->getName();
            }
            $report[] = "";
        }

        // Tablas eliminadas
        if (!empty($differences['dropped_tables'])) {
            $report[] = "🗑️ TABLAS ELIMINADAS (en remoto, no en local):";
            foreach ($differences['dropped_tables'] as $tableName) {
                $report[] = "  - " . $tableName;
            }
            $report[] = "";
        }

        // Tablas modificadas
        if (!empty($differences['altered_tables'])) {
            $report[] = "🔧 TABLAS MODIFICADAS:";
            foreach ($differences['altered_tables'] as $tableDiff) {
                $report[] = "  ~ " . $tableDiff->getOldTable()->getName();
                
                // Columnas añadidas
                foreach ($tableDiff->getAddedColumns() as $column) {
                    $report[] = "    + Columna: " . $column->getName() . " (" . $column->getType()->getName() . ")";
                }
                
                // Columnas eliminadas
                foreach ($tableDiff->getDroppedColumns() as $columnName) {
                    $report[] = "    - Columna: " . $columnName;
                }
                
                // Columnas modificadas
                foreach ($tableDiff->getModifiedColumns() as $columnDiff) {
                    $report[] = "    ~ Columna: " . $columnDiff->getOldColumn()->getName() . " (modificada)";
                }
            }
            $report[] = "";
        }

        if (empty($differences['new_tables']) && empty($differences['dropped_tables']) && empty($differences['altered_tables'])) {
            $report[] = "✅ Las estructuras son idénticas";
        }

        return implode("\n", $report);
    }

    private function generateJsonReport(Schema $localSchema, Schema $remoteSchema, array $differences, bool $onlyDifferences): string
    {
        $data = [
            'timestamp' => date('c'),
            'local_tables_count' => count($localSchema->getTables()),
            'remote_tables_count' => count($remoteSchema->getTables()),
            'differences' => [
                'new_tables' => array_map(fn($table) => $table->getName(), $differences['new_tables']),
                'dropped_tables' => $differences['dropped_tables'],
                'altered_tables' => array_map(fn($tableDiff) => $tableDiff->getOldTable()->getName(), $differences['altered_tables']),
            ]
        ];

        if (!$onlyDifferences) {
            $data['local_tables'] = array_map(fn($table) => $table->getName(), $localSchema->getTables());
            $data['remote_tables'] = array_map(fn($table) => $table->getName(), $remoteSchema->getTables());
        }

        return json_encode($data, JSON_PRETTY_PRINT);
    }

    private function generateSqlReport(array $differences): string
    {
        $sql = [];
        $sql[] = "-- SQL para sincronizar estructura";
        $sql[] = "-- Generado el: " . date('Y-m-d H:i:s');
        $sql[] = "";

        // Aquí podrías generar SQL específico basado en las diferencias
        // Por simplicidad, solo agregamos comentarios
        
        if (!empty($differences['new_tables'])) {
            $sql[] = "-- Tablas nuevas a crear:";
            foreach ($differences['new_tables'] as $table) {
                $sql[] = "-- CREATE TABLE " . $table->getName() . " (...);";
            }
            $sql[] = "";
        }

        return implode("\n", $sql);
    }

    private function showSummary(SymfonyStyle $io, array $differences, bool $onlyDifferences): void
    {
        $newTables = count($differences['new_tables']);
        $droppedTables = count($differences['dropped_tables']);
        $alteredTables = count($differences['altered_tables']);

        if ($newTables === 0 && $droppedTables === 0 && $alteredTables === 0) {
            $io->success('✅ Las estructuras son idénticas');
        } else {
            $io->warning('⚠️ Se encontraron diferencias:');
            if ($newTables > 0) $io->text("  🆕 Tablas nuevas: $newTables");
            if ($droppedTables > 0) $io->text("  🗑️ Tablas eliminadas: $droppedTables");
            if ($alteredTables > 0) $io->text("  🔧 Tablas modificadas: $alteredTables");
        }
    }
}
